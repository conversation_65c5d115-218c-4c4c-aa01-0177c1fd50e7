services:
  backend:
    build:
      context: .
      dockerfile: Dockerfile
      # Use cache for faster builds
      cache_from:
        - deer-flow-backend:latest
    container_name: deer-flow-backend
    # Internal port for communication with frontend
    expose:
      - "8000"
    env_file:
      - .env
    volumes:
      - ./conf.yaml:/app/conf.yaml:ro
      # Optional: mount logs directory for debugging
      - ./logs:/app/logs
    restart: unless-stopped
    networks:
      - deer-flow-network
    # Health check to ensure backend is ready
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  frontend:
    build:
      context: ./web
      dockerfile: Dockerfile
      args:
        - NEXT_PUBLIC_API_URL=${NEXT_PUBLIC_API_URL:-http://localhost:8000/api}
      # Use cache for faster builds
      cache_from:
        - deer-flow-frontend:latest
    container_name: deer-flow-frontend
    ports:
      - "3000:3000"
    env_file:
      - .env
    depends_on:
      backend:
        condition: service_started
    restart: unless-stopped
    networks:
      - deer-flow-network
    # Health check to ensure frontend is ready
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

networks:
  deer-flow-network:
    driver: bridge
