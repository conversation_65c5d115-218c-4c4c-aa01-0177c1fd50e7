#!/bin/bash

# Docker Build Script for DeerFlow
# This script optimizes the Docker build process with proper caching and error handling

set -e  # Exit on any error

echo "🦌 Starting DeerFlow Docker Build Process..."

# Check if .env file exists
if [ ! -f ".env" ]; then
    echo "⚠️  .env file not found. Copying from .env.example..."
    cp .env.example .env
    echo "✅ Please edit .env file with your configuration before running again."
    exit 1
fi

# Check if conf.yaml exists
if [ ! -f "conf.yaml" ]; then
    echo "⚠️  conf.yaml not found. Copying from conf.yaml.example..."
    cp conf.yaml.example conf.yaml
    echo "✅ Using default configuration. You can edit conf.yaml if needed."
fi

# Create logs directory if it doesn't exist
mkdir -p logs

# Load environment variables
source .env

# Set default API URL if not set
export NEXT_PUBLIC_API_URL=${NEXT_PUBLIC_API_URL:-"http://localhost:8000/api"}

echo "🔧 Configuration:"
echo "   API URL: $NEXT_PUBLIC_API_URL"
echo "   Search Engine: ${SEARCH_API:-tavily}"

# Build with docker-compose
echo "🏗️  Building Docker containers..."
docker-compose build --parallel

echo "🚀 Starting services..."
docker-compose up -d

echo "⏳ Waiting for services to be ready..."
sleep 10

# Check if services are running
if docker-compose ps | grep -q "Up"; then
    echo "✅ DeerFlow is now running!"
    echo "   Frontend: http://localhost:3000"
    echo "   Backend: http://localhost:8000 (internal)"
    echo ""
    echo "📝 To view logs:"
    echo "   docker-compose logs -f"
    echo ""
    echo "🛑 To stop:"
    echo "   docker-compose down"
else
    echo "❌ Failed to start services. Check logs:"
    docker-compose logs
    exit 1
fi
