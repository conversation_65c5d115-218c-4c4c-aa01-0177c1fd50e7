# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

import json
import logging
import os
from typing import Dict, List, Optional

import aiohttp
import requests
from langchain_tavily._utilities import TAVILY_API_URL
from langchain_tavily.tavily_search import (
    TavilySearchAPIWrapper as OriginalTavilySearchAPIWrapper,
)

logger = logging.getLogger(__name__)


class TavilyQuotaExhaustedException(Exception):
    """Exception raised when all Tavily API keys have exhausted their quota."""

    pass


class EnhancedTavilySearchAPIWrapper(OriginalTavilySearchAPIWrapper):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._api_keys = self._get_api_keys()

    def _get_api_keys(self) -> List[str]:
        """Get list of API keys from environment variable or single key."""
        # First try to get from the original tavily_api_key
        primary_key = self.tavily_api_key.get_secret_value()

        # Then check for multiple keys in TAVILY_API_KEYS (comma-separated)
        multiple_keys_str = os.getenv("TAVILY_API_KEYS", "")
        if multiple_keys_str:
            keys = [key.strip() for key in multiple_keys_str.split(",") if key.strip()]
            logger.info(f"Found {len(keys)} Tavily API keys for rotation")
            return keys
        else:
            logger.info("Using single Tavily API key")
            return [primary_key]

    def _is_quota_error(self, status_code: int, response_text: str) -> bool:
        """Check if the error indicates quota exhaustion."""
        if status_code in [432, 433]:
            return True

        # Check for quota-related error messages
        quota_messages = [
            "exceeds your plan's set usage limit",
            "exceeds the pay-as-you-go limit",
            "quota exceeded",
            "usage limit",
        ]
        return any(msg in response_text.lower() for msg in quota_messages)

    def _make_request_with_rotation(self, params_template: Dict) -> Dict:
        """Make request with API key rotation on quota errors."""
        last_error = None

        for i, api_key in enumerate(self._api_keys):
            params = params_template.copy()
            params["api_key"] = api_key

            try:
                logger.debug(f"Trying Tavily API key {i + 1}/{len(self._api_keys)}")
                response = requests.post(f"{TAVILY_API_URL}/search", json=params)

                if response.status_code == 200:
                    logger.debug(f"Successfully used API key {i + 1}")
                    return response.json()
                elif self._is_quota_error(response.status_code, response.text):
                    logger.warning(
                        f"API key {i + 1} quota exhausted: {response.status_code} - {response.text}"
                    )
                    last_error = f"Key {i + 1}: {response.text}"
                    continue
                else:
                    # Non-quota error, raise immediately
                    response.raise_for_status()

            except requests.RequestException as e:
                logger.error(f"Request error with API key {i + 1}: {e}")
                last_error = str(e)
                continue

        # All keys exhausted
        raise TavilyQuotaExhaustedException(
            f"本月联网查询额度已经消耗完毕。所有 {len(self._api_keys)} 个API密钥都已达到使用限制。最后错误: {last_error}"
        )

    def raw_results(
        self,
        query: str,
        max_results: Optional[int] = 5,
        search_depth: Optional[str] = "advanced",
        include_domains: Optional[List[str]] = [],
        exclude_domains: Optional[List[str]] = [],
        include_answer: Optional[bool] = False,
        include_raw_content: Optional[bool] = False,
        include_images: Optional[bool] = False,
        include_image_descriptions: Optional[bool] = False,
    ) -> Dict:
        params_template = {
            "query": query,
            "max_results": max_results,
            "search_depth": search_depth,
            "include_domains": include_domains,
            "exclude_domains": exclude_domains,
            "include_answer": include_answer,
            "include_raw_content": include_raw_content,
            "include_images": include_images,
            "include_image_descriptions": include_image_descriptions,
        }

        return self._make_request_with_rotation(params_template)

    async def _make_request_with_rotation_async(self, params_template: Dict) -> Dict:
        """Make async request with API key rotation on quota errors."""
        last_error = None

        for i, api_key in enumerate(self._api_keys):
            params = params_template.copy()
            params["api_key"] = api_key

            try:
                logger.debug(
                    f"Trying Tavily API key {i + 1}/{len(self._api_keys)} (async)"
                )
                async with aiohttp.ClientSession(trust_env=True) as session:
                    async with session.post(
                        f"{TAVILY_API_URL}/search", json=params
                    ) as response:
                        if response.status == 200:
                            logger.debug(f"Successfully used API key {i + 1} (async)")
                            data = await response.text()
                            return json.loads(data)
                        elif self._is_quota_error(
                            response.status, await response.text()
                        ):
                            response_text = await response.text()
                            logger.warning(
                                f"API key {i + 1} quota exhausted (async): {response.status} - {response_text}"
                            )
                            last_error = f"Key {i + 1}: {response_text}"
                            continue
                        else:
                            # Non-quota error, raise immediately
                            response.raise_for_status()

            except aiohttp.ClientError as e:
                logger.error(f"Request error with API key {i + 1} (async): {e}")
                last_error = str(e)
                continue

        # All keys exhausted
        raise TavilyQuotaExhaustedException(
            f"本月联网查询额度已经消耗完毕。所有 {len(self._api_keys)} 个API密钥都已达到使用限制。最后错误: {last_error}"
        )

    async def raw_results_async(
        self,
        query: str,
        max_results: Optional[int] = 5,
        search_depth: Optional[str] = "advanced",
        include_domains: Optional[List[str]] = [],
        exclude_domains: Optional[List[str]] = [],
        include_answer: Optional[bool] = False,
        include_raw_content: Optional[bool] = False,
        include_images: Optional[bool] = False,
        include_image_descriptions: Optional[bool] = False,
    ) -> Dict:
        """Get results from the Tavily Search API asynchronously with key rotation."""
        params_template = {
            "query": query,
            "max_results": max_results,
            "search_depth": search_depth,
            "include_domains": include_domains,
            "exclude_domains": exclude_domains,
            "include_answer": include_answer,
            "include_raw_content": include_raw_content,
            "include_images": include_images,
            "include_image_descriptions": include_image_descriptions,
        }

        return await self._make_request_with_rotation_async(params_template)

    def clean_results_with_images(
        self, raw_results: Dict[str, List[Dict]]
    ) -> List[Dict]:
        results = raw_results["results"]
        """Clean results from Tavily Search API."""
        clean_results = []
        for result in results:
            clean_result = {
                "type": "page",
                "title": result["title"],
                "url": result["url"],
                "content": result["content"],
                "score": result["score"],
            }
            if raw_content := result.get("raw_content"):
                clean_result["raw_content"] = raw_content
            clean_results.append(clean_result)
        images = raw_results["images"]
        for image in images:
            clean_result = {
                "type": "image",
                "image_url": image["url"],
                "image_description": image["description"],
            }
            clean_results.append(clean_result)
        return clean_results
