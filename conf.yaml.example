# [!NOTE]
# Read the `docs/configuration_guide.md` carefully, and update the
# configurations to match your specific settings and requirements.
# - Replace `api_key` with your own credentials.
# - Replace `base_url` and `model` name if you want to use a custom model.
# - Set `verify_ssl` to `false` if your LLM server uses self-signed certificates
# - A restart is required every time you change the `config.yaml` file.

BASIC_MODEL:
  base_url: https://api.siliconflow.cn/v1
  model: "Qwen/Qwen3-8B"
  api_key: sk-iopewlrhdjifcztkffeqpkdskyicedpmpwtqsltysfvlpbee
  # max_retries: 3 # Maximum number of retries for LLM calls
  # verify_ssl: false  # Uncomment this line to disable SSL certificate verification for self-signed certificates
 
# Reasoning model is optional.
# Uncomment the following settings if you want to use reasoning model
# for planning.

# REASONING_MODEL:
#   base_url: https://ark.cn-beijing.volces.com/api/v3
#   model: "doubao-1-5-thinking-pro-m-250428"
#   api_key: xxxx
#   max_retries: 3 # Maximum number of retries for LLM calls


# OTHER SETTINGS:
# Search engine configuration (Only supports Tavily currently)
# SEARCH_ENGINE:
#   engine: tavily
#   # Only include results from these domains
#   include_domains:
#     - example.com
#     - trusted-news.com
#     - reliable-source.org
#     - gov.cn
#     - edu.cn
#   # Exclude results from these domains
#   exclude_domains:
#     - example.com
