@echo off
REM Docker Build Script for DeerFlow (Windows)
REM This script optimizes the Docker build process with proper caching and error handling

echo 🦌 Starting DeerFlow Docker Build Process...

REM Check if .env file exists
if not exist ".env" (
    echo ⚠️  .env file not found. Copying from .env.example...
    copy .env.example .env
    echo ✅ Please edit .env file with your configuration before running again.
    pause
    exit /b 1
)

REM Check if conf.yaml exists
if not exist "conf.yaml" (
    echo ⚠️  conf.yaml not found. Copying from conf.yaml.example...
    copy conf.yaml.example conf.yaml
    echo ✅ Using default configuration. You can edit conf.yaml if needed.
)

REM Create logs directory if it doesn't exist
if not exist "logs" mkdir logs

REM Set default API URL if not set
if "%NEXT_PUBLIC_API_URL%"=="" set NEXT_PUBLIC_API_URL=http://localhost:8000/api

echo 🔧 Configuration:
echo    API URL: %NEXT_PUBLIC_API_URL%
echo    Search Engine: %SEARCH_API%

REM Build with docker-compose
echo 🏗️  Building Docker containers...
docker-compose build --parallel

echo 🚀 Starting services...
docker-compose up -d

echo ⏳ Waiting for services to be ready...
timeout /t 10 /nobreak > nul

REM Check if services are running
docker-compose ps | findstr "Up" > nul
if %errorlevel% equ 0 (
    echo ✅ DeerFlow is now running!
    echo    Frontend: http://localhost:3000
    echo    Backend: http://localhost:8000 ^(internal^)
    echo.
    echo 📝 To view logs:
    echo    docker-compose logs -f
    echo.
    echo 🛑 To stop:
    echo    docker-compose down
) else (
    echo ❌ Failed to start services. Check logs:
    docker-compose logs
    pause
    exit /b 1
)

pause
