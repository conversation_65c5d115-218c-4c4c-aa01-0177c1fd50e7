# Docker Deployment Guide for DeerFlow

This guide explains how to deploy DeerFlow using Docker with optimized build processes.

## Quick Start

### Prerequisites
- Docker and Docker Compose installed
- At least 4GB of available RAM
- 10GB of free disk space

### Automated Deployment

#### Linux/macOS
```bash
chmod +x docker-build.sh
./docker-build.sh
```

#### Windows
```cmd
docker-build.bat
```

### Manual Deployment

1. **Prepare Configuration Files**
   ```bash
   # Copy environment configuration
   cp .env.example .env
   
   # Copy model configuration (optional, defaults provided)
   cp conf.yaml.example conf.yaml
   ```

2. **Edit Configuration**
   - Edit `.env` file with your API keys and settings
   - Optionally edit `conf.yaml` for model configurations

3. **Build and Start Services**
   ```bash
   docker-compose up --build -d
   ```

4. **Verify Deployment**
   ```bash
   # Check service status
   docker-compose ps
   
   # View logs
   docker-compose logs -f
   ```

## Configuration

### Environment Variables

Key environment variables in `.env`:

```bash
# API Configuration
NEXT_PUBLIC_API_URL=http://localhost:8000/api

# Search Engine
SEARCH_API=tavily
TAVILY_API_KEY=your-tavily-key
# For multiple keys (automatic rotation)
TAVILY_API_KEYS=key1,key2,key3

# LLM Configuration (using environment variables)
BASIC_MODEL__base_url=https://api.siliconflow.cn/v1
BASIC_MODEL__model=Qwen/Qwen3-8B
BASIC_MODEL__api_key=your-api-key
```

### Model Configuration

The `conf.yaml` file contains default model configurations:

```yaml
BASIC_MODEL:
  base_url: https://api.siliconflow.cn/v1
  model: "Qwen/Qwen3-8B"
  api_key: sk-iopewlrhdjifcztkffeqpkdskyicedpmpwtqsltysfvlpbee
```

## Features

### Optimized Build Process
- **Multi-stage builds** for smaller final images
- **Build caching** for faster subsequent builds
- **Parallel builds** for backend and frontend
- **Health checks** for service readiness

### Tavily API Key Rotation
- Automatic rotation when quota is exhausted
- Support for multiple API keys
- Graceful error handling with user-friendly messages

### Security
- Non-root user in containers
- Read-only configuration mounts
- Internal network communication

## Troubleshooting

### Common Issues

1. **Build Failures**
   ```bash
   # Clear Docker cache and rebuild
   docker system prune -a
   docker-compose build --no-cache
   ```

2. **Service Not Starting**
   ```bash
   # Check logs
   docker-compose logs backend
   docker-compose logs frontend
   ```

3. **Configuration Issues**
   ```bash
   # Verify configuration files
   cat .env
   cat conf.yaml
   ```

### Health Checks

Services include health checks that verify:
- Backend API is responding
- Frontend is serving content
- Database connections (if applicable)

### Logs

View logs for debugging:
```bash
# All services
docker-compose logs -f

# Specific service
docker-compose logs -f backend
docker-compose logs -f frontend
```

## Maintenance

### Updates
```bash
# Pull latest changes
git pull

# Rebuild and restart
docker-compose up --build -d
```

### Cleanup
```bash
# Stop services
docker-compose down

# Remove volumes (caution: this removes data)
docker-compose down -v

# Clean up unused Docker resources
docker system prune
```

## Performance Tips

1. **Allocate sufficient resources** to Docker
2. **Use SSD storage** for better I/O performance
3. **Monitor resource usage** with `docker stats`
4. **Regular cleanup** of unused images and containers

## Support

For issues and questions:
1. Check the logs first
2. Verify configuration files
3. Consult the main README.md
4. Open an issue on GitHub
